/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantadmin;

import com.frt.generalgw.config.FeignConfig;
import com.frt.generalgw.domain.param.*;
import com.frt.generalgw.domain.result.CommonResult;
import com.frt.generalgw.domain.result.MerchantRolePermissionListResult;
import com.frt.generalgw.domain.result.RoleDetailQueryResult;
import com.frt.generalgw.domain.result.RoleListQueryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version RoleManagerClient.java, v 0.1 2025-08-27 15:24 zhangling
 */
@FeignClient(value = "frt-usercore-dev", configuration = {FeignConfig.class})
public interface RoleManagerClient {

    /**
     * 查询角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @PostMapping("/api/role/query-role-list")
    RoleListQueryResult getRoleList(@RequestBody RoleListQueryParam param);

    /**
     * 查询角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @PostMapping("/api/role/get-role-detail")
    RoleDetailQueryResult getRoleDetail(@RequestBody RoleDetailQueryParam param);

    /**
     * 添加角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/role/add-role")
    CommonResult addRole(@RequestBody RoleAddParam param);

    /**
     * 更新角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/role/update-role")
    CommonResult updateRole(@RequestBody RoleUpdateParam param);

    /**
     * 删除角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/role/delete-role")
    CommonResult deleteRole(@RequestBody RoleDeleteParam param);

    /**
     * 获取权限菜单模板
     * @param param
     * @return
     */
    @PostMapping("/api/role/menu-permission-template")
    MerchantRolePermissionListResult getPermissionTemplate(@RequestBody MerchantMenuPermissionParam param);
}