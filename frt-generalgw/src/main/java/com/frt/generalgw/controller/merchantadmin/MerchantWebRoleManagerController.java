/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantadmin;

import com.frt.generalgw.client.usercore.merchantadmin.RoleManagerClient;
import com.frt.generalgw.domain.param.*;
import com.frt.generalgw.domain.result.CommonResult;
import com.frt.generalgw.domain.result.MerchantRolePermissionListResult;
import com.frt.generalgw.domain.result.RoleDetailQueryResult;
import com.frt.generalgw.domain.result.RoleListQueryResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/RoleManagerController
 *
 * <AUTHOR>
 * @version RoleManagerController.java, v 0.1 2025-08-27 14:52 zhangling
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/merchant/web/role")
public class MerchantWebRoleManagerController {

    private final RoleManagerClient roleManagerClient;

    /**
     * 查询角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @PostMapping("/role-list")
    public RoleListQueryResult getRoleList(@RequestBody RoleListQueryParam param) {
        // 实际实现中，这里应该调用服务层获取角色列表数据
        // 目前仅返回一个空的结果对象
        return roleManagerClient.getRoleList(param);
    }

    /**
     * 查询角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @PostMapping("/role-detail")
    public RoleDetailQueryResult getRoleDetail(@RequestBody RoleDetailQueryParam param) {
        // 实际实现中，这里应该调用服务层获取角色详情数据
        // 目前仅返回一个空的结果对象
        return roleManagerClient.getRoleDetail(param);
    }

    /**
     * 添加角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/role-add")
    public CommonResult addRole(@RequestBody RoleAddParam param) {
        // 实际实现中，这里应该调用服务层添加角色
        // 目前仅返回成功结果
        CommonResult result = new CommonResult();
        result.setCode(0);
        result.setMessage("操作成功");
        return roleManagerClient.addRole(param);
    }

    /**
     * 更新角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/role-update")
    public CommonResult updateRole(@RequestBody RoleUpdateParam param) {
        // 实际实现中，这里应该调用服务层更新角色
        // 目前仅返回成功结果
        CommonResult result = new CommonResult();
        result.setCode(0);
        result.setMessage("操作成功");
        return roleManagerClient.updateRole(param);
    }

    /**
     * 删除角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/role-delete")
    public CommonResult deleteRole(@RequestBody RoleDeleteParam param) {
        // 实际实现中，这里应该调用服务层删除角色
        // 目前仅返回成功结果
        CommonResult result = new CommonResult();
        result.setCode(0);
        result.setMessage("操作成功");
        return roleManagerClient.deleteRole(param);
    }

    /**
     * 获取权限模版
     */
    @PostMapping("/get-permission-template")
    public MerchantRolePermissionListResult getPermissionTemplate(@RequestBody MerchantMenuPermissionParam param) {
        return roleManagerClient.getPermissionTemplate(param);
    }
}