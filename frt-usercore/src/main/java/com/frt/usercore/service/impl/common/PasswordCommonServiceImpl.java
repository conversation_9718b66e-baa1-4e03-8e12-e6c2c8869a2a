package com.frt.usercore.service.impl.common;

import com.frt.usercore.domain.result.common.CheckLoginPasswordResult;
import com.frt.usercore.service.common.PasswordCommonService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class PasswordCommonServiceImpl implements PasswordCommonService {


	/**
	 * 校验登录密码
	 *
	 * @param account      账号
	 * @param password     密码
	 * @param platformType 平台类型 1-运营后台 2-代理商 3-商户
	 * @param tenantId     租户ID
	 * @return 校验结果
	 */
	@Override
	public CheckLoginPasswordResult checkLoginPassword(String account, String password, String platformType, String tenantId) {
		return null;
	}
}
