package com.frt.usercore.service.impl.storemanager;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.frt.usercore.dao.entity.UnityCategoryDO;
import com.frt.usercore.dao.repository.MerchantStoreInfoDAO;
import com.frt.usercore.dao.repository.UnityCategoryDAO;
import com.frt.usercore.domain.mapper.StoreManagerMapper;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoAddParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoQueryParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoUpdateParam;
import com.frt.usercore.domain.param.storemanager.StoreListQueryParam;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoAddResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoQueryResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoUpdateResult;
import com.frt.usercore.domain.result.storemanager.StoreListQueryResult;
import com.frt.usercore.service.storemanager.StoreManagerService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class StoreManagerServiceImpl implements StoreManagerService {

    @Autowired
    private MerchantStoreInfoDAO merchantStoreInfoDAO;

    @Autowired
    private StoreManagerMapper storeManagerMapper;
    @Autowired
    private UnityCategoryDAO unityCategoryDAO;

    @Override
    public PageResult<StoreListQueryResult> queryStoreList(PageParam<StoreListQueryParam> param) {
        Page<MerchantStoreInfoDO> pageDO = merchantStoreInfoDAO.findStorePage(param);
        if (CollectionUtil.isEmpty(pageDO.getRecords())) {
            PageResult<StoreListQueryResult> pageResult = new PageResult<>();
            pageResult.setTotal(pageDO.getTotal());
            pageResult.setSize(pageDO.getSize());
            pageResult.setCurrent(pageDO.getCurrent());
            pageResult.setRecords(Lists.newArrayList());
            return pageResult;
        }

        List<StoreListQueryResult> resultList = new ArrayList<>();
        List<MerchantStoreInfoDO> records = pageDO.getRecords();
        for (MerchantStoreInfoDO item : records) {
            StoreListQueryResult result = storeManagerMapper.coverMerchantStoreInfoDOToStoreListQueryResult(item);
            resultList.add(result);
        }
        PageResult<StoreListQueryResult> pageResult = new PageResult<>();
        pageResult.setTotal(pageDO.getTotal());
        pageResult.setSize(pageDO.getSize());
        pageResult.setCurrent(pageDO.getCurrent());
        pageResult.setRecords(resultList);
        return pageResult;
    }

    @Override
    public StoreInfoAddResult addStoreInfo(StoreInfoAddParam param) {
        MerchantStoreInfoDO storeInfoDO = storeManagerMapper.coverStoreInfoAddParamToStoreInfoDO(param);
        storeInfoDO.setTenantId(TenantContextUtil.getTenantId());
        merchantStoreInfoDAO.save(storeInfoDO);
        StoreInfoAddResult result = new StoreInfoAddResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    public StoreInfoUpdateResult updateStoreInfo(StoreInfoUpdateParam param) {
        MerchantStoreInfoDO dbStoreInfoDO = merchantStoreInfoDAO.getInfoByStoreId(param.getStoreId());
        if (null == dbStoreInfoDO) {
            throw ValidateUtil.validateMsg("门店信息不存在");
        }

        MerchantStoreInfoDO storeInfoDO = storeManagerMapper.coverStoreInfoUpdateParamToStoreInfoDO(param);
        merchantStoreInfoDAO.updateById(storeInfoDO);
        StoreInfoUpdateResult result = new StoreInfoUpdateResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    public StoreInfoQueryResult queryStoreInfo(StoreInfoQueryParam param) {
        MerchantStoreInfoDO infoDO = merchantStoreInfoDAO.getInfoByStoreId(param.getStoreId());
        if (infoDO == null) {
            StoreInfoQueryResult result = new StoreInfoQueryResult();
            return result;
        }

        StoreInfoQueryResult result = storeManagerMapper.coverStoreInfoUpdateParamToStoreInfoQueryResult(infoDO);
        Integer unityCatId = result.getUnityCatId();
        result.setLevel1CategoryId(0);
        result.setLevel1CategoryName("未知");
        result.setUnityCatId(0);
        result.setUnityCatName("未知");
        if (unityCatId != null) {
            UnityCategoryDO secondCategoryDO = unityCategoryDAO.getInfoById(unityCatId);
            if (null != secondCategoryDO) {
                UnityCategoryDO firstCategoryDO = unityCategoryDAO.getInfoById(secondCategoryDO.getParentId());
                if (null != firstCategoryDO) {
                    result.setLevel1CategoryId(firstCategoryDO.getId());
                    result.setLevel1CategoryName(firstCategoryDO.getCatName());
                    result.setUnityCatId(secondCategoryDO.getId());
                    result.setUnityCatName(secondCategoryDO.getCatName());
                }
            }
        }
        return result;
    }
}
