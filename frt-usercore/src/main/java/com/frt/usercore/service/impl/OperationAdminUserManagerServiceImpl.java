package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.common.enums.business.OperationAdminSearchTypeEnum;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.enums.exception.base.ErrorCodeEnum;
import com.frt.usercore.common.exception.InternalException;
import com.frt.usercore.common.utils.IdWorkerUtil;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.common.utils.PasswordUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.AccountBindRoleDO;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.entity.OperationUserDO;
import com.frt.usercore.dao.entity.TenantRoleDO;
import com.frt.usercore.dao.repository.AccountBindRoleDAO;
import com.frt.usercore.dao.repository.AccountDAO;
import com.frt.usercore.dao.repository.OperationUserDAO;
import com.frt.usercore.dao.repository.TenantRoleDAO;
import com.frt.usercore.domain.dto.param.UserListQueryParamDTO;
import com.frt.usercore.domain.dto.result.FindRoleByUserIdListResultDTO;
import com.frt.usercore.domain.mapper.OperationAdminUserManagerServiceObjMapper;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserAddParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserDetailQueryParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserListQueryParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserModifyParam;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.operationadmin.usermanager.UserDetailQueryResult;
import com.frt.usercore.domain.result.operationadmin.usermanager.UserListQueryResult;
import com.frt.usercore.service.OperationAdminUserManagerService;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 运营后台用户管理服务实现类
 *
 * <AUTHOR>
 * @version OperationAdminUserManagerServiceImpl.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationAdminUserManagerServiceImpl implements OperationAdminUserManagerService {

    private final OperationAdminUserManagerServiceObjMapper mapper;
    private final AccountDAO accountDAO;
    private final TenantRoleDAO tenantRoleDAO;
    private final AccountBindRoleDAO accountBindRoleDAO;
    private final OperationUserDAO operationUserDAO;
    private final TransactionTemplate transactionTemplate;


    /**
     * 员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @Override
    public PageResult<UserListQueryResult> getUserList(PageParam<UserListQueryParam> param) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.getUserList >> 接口开始 >> param = {}", JSON.toJSONString(param));
        PageParam<UserListQueryParamDTO> pageDTO = buildUserListQueryParamDTOPage(param);
        Page<AccountDO> pageList = accountDAO.findOperationAdminPageList(pageDTO);
        return buildUserListQueryResultPageResult(pageList);
    }



    /**
     * 新增员工
     *
     * @param param 请求参数
     */
    @Override
    public void addUser(UserAddParam param) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.addUser >> 接口开始 >> param = {}", JSON.toJSONString(param));

        try {
            // 参数校验
            validateAddUserParam(param);

            String tenantId = TenantContextUtil.getTenantId();

            // 检查账号是否已存在
            checkAccountExists(param.getUsername(), tenantId);

            // 检查角色是否存在且有效
            checkRoleExists(param.getRoleId());

            // 使用事务创建用户相关信息
            transactionTemplate.execute(status -> {
                try {
                    // 生成用户ID
                    String newUserId = IdWorkerUtil.getSingleId();

                    // 创建账号信息
                    createAccount(param, tenantId, newUserId);

                    // 创建运营用户信息
                    createOperationUser(param, tenantId, newUserId);

                    // 绑定用户角色关系
                    bindUserRole(newUserId, param.getRoleId());

                    return Boolean.TRUE;
                } catch (Exception e) {
                    LogUtil.error(log, "OperationAdminUserManagerServiceImpl.addUser >> 事务执行失败 >> error = {}", e.getMessage(), e);
                    status.setRollbackOnly();
                    throw e;
                }
            });

        } catch (Exception e) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.addUser >> 新增员工失败 >> error = {}", e.getMessage(), e);
            throw e;
        }

        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.addUser >> 接口结束");
    }

    /**
     * 修改员工
     *
     * @param param 请求参数
     */
    @Override
    public void modifyUser(UserModifyParam param) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.modifyUser >> 接口开始 >> param = {}", JSON.toJSONString(param));

        try {
            // 参数校验
            validateModifyUserParam(param);

            // 检查用户是否存在
            AccountDO existAccount = checkUserExists(param.getUserId());

            // 如果修改了角色，检查角色是否存在且有效
            if (StrUtil.isNotBlank(param.getRoleId())) {
                checkRoleExists(param.getRoleId());
            }

            // 使用事务更新用户相关信息
            transactionTemplate.execute(status -> {
                try {
                    // 更新账号信息
                    updateAccount(param, existAccount);

                    // 更新运营用户信息
                    updateOperationUser(param);

                    // 更新用户角色关系
                    if (StrUtil.isNotBlank(param.getRoleId())) {
                        updateUserRole(param.getUserId(), param.getRoleId());
                    }

                    return Boolean.TRUE;
                } catch (Exception e) {
                    LogUtil.error(log, "OperationAdminUserManagerServiceImpl.modifyUser >> 事务执行失败 >> error = {}", e.getMessage(), e);
                    status.setRollbackOnly();
                    throw e;
                }
            });

            LogUtil.info(log, "OperationAdminUserManagerServiceImpl.modifyUser >> 修改员工成功 >> userId = {}", param.getUserId());
        } catch (Exception e) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.modifyUser >> 修改员工失败 >> error = {}", e.getMessage(), e);
            throw e;
        }

        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.modifyUser >> 接口结束");
    }

    /**
     * 查询员工信息
     *
     * @param param 请求参数
     * @return 员工信息
     */
    @Override
    public UserDetailQueryResult getUserDetail(UserDetailQueryParam param) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.getUserDetail >> 接口开始 >> param = {}", JSON.toJSONString(param));

        try {
            // 参数校验
            validateUserDetailQueryParam(param);

            // 查询用户基本信息
            AccountDO accountDO = checkUserExists(param.getUserId());

            // 查询用户角色信息
            AccountBindRoleDO userRole = accountBindRoleDAO.getByUserId(param.getUserId());
            String roleName = null;
            if (userRole != null && StrUtil.isNotBlank(userRole.getRoleId())) {
                TenantRoleDO role = tenantRoleDAO.getByRoleId(userRole.getRoleId());
                if (role != null) {
                    roleName = role.getRoleName();
                }
            }

            // 构建返回结果
            UserDetailQueryResult result = buildUserDetailQueryResult(accountDO, userRole, roleName);

            LogUtil.info(log, "OperationAdminUserManagerServiceImpl.getUserDetail >> 查询员工信息成功 >> userId = {}", param.getUserId());
            return result;
        } catch (Exception e) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.getUserDetail >> 查询员工信息失败 >> error = {}", e.getMessage(), e);
            throw e;
        } finally {
            LogUtil.info(log, "OperationAdminUserManagerServiceImpl.getUserDetail >> 接口结束");
        }
    }


    /**
     * 构建UserListQueryResultPageResult
     *
     * @param pageList pageList
     * @return PageResult<UserListQueryResultDTO>
     */
    private PageResult<UserListQueryResult> buildUserListQueryResultPageResult(Page<AccountDO> pageList) {
        List<AccountDO> accountDOList = pageList.getRecords();
        if (CollectionUtil.isEmpty(accountDOList)) {
            return new PageResult<>();
        }
        List<String> userIdList = accountDOList.stream().map(AccountDO::getUserId).toList();
        // 根据 userId 查询角色名称
        List<FindRoleByUserIdListResultDTO> userRoleList = tenantRoleDAO.findRoleByUserIdList(userIdList);
        // 使用 stream 转换为 map
        Map<String, String> userRoleMap = userRoleList.stream().collect(Collectors.toMap(FindRoleByUserIdListResultDTO::getUserId, FindRoleByUserIdListResultDTO::getRoleName));
        PageResult<UserListQueryResult> pageResult = new PageResult<>();
        pageResult.setTotal(pageList.getTotal());
        pageResult.setSize(pageList.getSize());
        pageResult.setCurrent(pageList.getCurrent());
        List<UserListQueryResult> resultList = mapper.toUserListQueryResultDTOList(accountDOList);
        resultList.forEach(item -> item.setRoleTypeName(userRoleMap.getOrDefault(item.getUserId(), StringPool.EMPTY)));
        pageResult.setRecords(resultList);
        return pageResult;
    }

    /**
     * 构建UserListQueryParamDTOPage
     *
     * @param param param
     * @return PageParam<UserListQueryParamDTO>
     */
    private PageParam<UserListQueryParamDTO> buildUserListQueryParamDTOPage(PageParam<UserListQueryParam> param) {
        PageParam<UserListQueryParamDTO> pageDTO = new PageParam<>(param.getPage(), param.getPageSize());
        UserListQueryParam queryParam = param.getQuery();

        UserListQueryParamDTO queryDTO = new UserListQueryParamDTO();
        String tenantId = TenantContextUtil.getTenantId();
        queryDTO.setTenantId(tenantId);
        queryDTO.setAccountStatus(queryParam.getAccountStatus());
        queryDTO.setPlatformType(PlatformEnum.OPERATION.getCode());
        OperationAdminSearchTypeEnum searchTypeEnum = OperationAdminSearchTypeEnum.getByCode(queryParam.getSearchType());
        if (searchTypeEnum == null) {
            pageDTO.setQuery(queryDTO);
            return pageDTO;
        }
        switch (searchTypeEnum) {
            case ACCOUNT_NAME:
                queryDTO.setAccount(queryParam.getSearchContent());
                break;
            case ACCOUNT_USER_NAME:
                queryDTO.setUserName(queryParam.getSearchContent());
                break;
            case ACCOUNT_ID:
                queryDTO.setUserId(queryParam.getSearchContent());
                break;
            case EMPLOYEE_PHONE:
                queryDTO.setPhone(queryParam.getSearchContent());
                break;
            case ROLE_ID:
                queryDTO.setRoleId(queryParam.getSearchContent());
                break;
            default:
                break;
        }
        pageDTO.setQuery(queryDTO);
        return pageDTO;
    }

    /**
     * 校验新增员工参数
     *
     * @param param 新增员工参数
     */
    private void validateAddUserParam(UserAddParam param) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.validateAddUserParam >> 开始校验参数");

        if (param == null) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "参数不能为空");
        }

        if (StrUtil.isBlank(param.getUsername())) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "员工账号不能为空");
        }

        if (StrUtil.isBlank(param.getPassword())) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "密码不能为空");
        }

        if (StrUtil.isBlank(param.getName())) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "员工姓名不能为空");
        }

        if (StrUtil.isBlank(param.getPhone())) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "手机号不能为空");
        }

        if (StrUtil.isBlank(param.getRoleId())) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "角色ID不能为空");
        }

        if (param.getAccountStatus() == null) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "账号状态不能为空");
        }

        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.validateAddUserParam >> 参数校验通过");
    }

    /**
     * 校验修改员工参数
     *
     * @param param 修改员工参数
     */
    private void validateModifyUserParam(UserModifyParam param) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.validateModifyUserParam >> 开始校验参数");

        if (param == null) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "参数不能为空");
        }

        if (StrUtil.isBlank(param.getUserId())) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "员工ID不能为空");
        }

        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.validateModifyUserParam >> 参数校验通过");
    }

    /**
     * 校验查询员工详情参数
     *
     * @param param 查询员工详情参数
     */
    private void validateUserDetailQueryParam(UserDetailQueryParam param) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.validateUserDetailQueryParam >> 开始校验参数");

        if (param == null) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "参数不能为空");
        }

        if (StrUtil.isBlank(param.getUserId())) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "员工ID不能为空");
        }

        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.validateUserDetailQueryParam >> 参数校验通过");
    }

    /**
     * 检查账号是否已存在
     *
     * @param username 用户名
     * @param tenantId 租户ID
     */
    private void checkAccountExists(String username, String tenantId) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.checkAccountExists >> 检查账号是否存在 >> username = {}", username);

        AccountDO existAccount = accountDAO.selectByAccountAndPlatformType(username, PlatformEnum.OPERATION.getCode(), tenantId);
        if (existAccount != null) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.checkAccountExists >> 账号已存在 >> username = {}", username);
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "账号已存在");
        }

        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.checkAccountExists >> 账号不存在，可以使用");
    }

    /**
     * 检查角色是否存在且有效
     *
     * @param roleId 角色ID
     */
    private void checkRoleExists(String roleId) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.checkRoleExists >> 检查角色是否存在 >> roleId = {}", roleId);

        TenantRoleDO role = tenantRoleDAO.getByRoleId(roleId);
        if (role == null) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.checkRoleExists >> 角色不存在 >> roleId = {}", roleId);
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "角色不存在");
        }

        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.checkRoleExists >> 角色存在且有效");
    }

    /**
     * 检查用户是否存在
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    private AccountDO checkUserExists(String userId) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.checkUserExists >> 检查用户是否存在 >> userId = {}", userId);

        AccountDO account = accountDAO.getByUserId(userId);
        if (account == null) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.checkUserExists >> 用户不存在 >> userId = {}", userId);
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), "用户不存在");
        }

        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.checkUserExists >> 用户存在");
        return account;
    }

    /**
     * 创建账号信息
     *
     * @param param    新增员工参数
     * @param tenantId 租户ID
     * @param userId   用户ID
     */
    private void createAccount(UserAddParam param, String tenantId, String userId) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.createAccount >> 开始创建账号 >> userId = {}", userId);

        try {
            // 生成密码和盐值（假设传入的是MD5密码）
            PasswordUtil.PasswordResult passwordResult = PasswordUtil.generatePassword(param.getPassword(), false);

            // 构建账号实体
            AccountDO accountDO = new AccountDO();
            accountDO.setUserId(userId);
            accountDO.setAccount(param.getUsername());
            accountDO.setPassword(passwordResult.getPassword());
            accountDO.setSalt(passwordResult.getSalt());
            accountDO.setAccountStatus(param.getAccountStatus());
            accountDO.setPlatformType(PlatformEnum.OPERATION.getCode());
            accountDO.setPlatformId(tenantId);
            accountDO.setIsAdmin(0); // 默认不是管理员
            accountDO.setIsResetPwd(0); // 默认未重置密码
            accountDO.setPhone(param.getPhone());
            accountDO.setName(param.getName());
            accountDO.setLastLoginTime(0);
            accountDO.setCreateTime(new Date());
            accountDO.setUpdateTime(new Date());
            accountDO.setIsDel(0);
            accountDO.setTenantId(tenantId);

            // 保存账号信息
            boolean saveResult = accountDAO.save(accountDO);
            if (!saveResult) {
                LogUtil.error(log, "OperationAdminUserManagerServiceImpl.createAccount >> 保存账号信息失败");
                throw new InternalException(ErrorCodeEnum.DATABASE_HANDLE_ERROR.getErrorCode(), "保存账号信息失败");
            }

            LogUtil.info(log, "OperationAdminUserManagerServiceImpl.createAccount >> 创建账号成功");
        } catch (Exception e) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.createAccount >> 创建账号失败 >> error = {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 绑定用户角色关系
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    private void bindUserRole(String userId, String roleId) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.bindUserRole >> 开始绑定用户角色 >> userId = {}, roleId = {}", userId, roleId);

        try {
            AccountBindRoleDO bindRole = new AccountBindRoleDO();
            bindRole.setUserId(userId);
            bindRole.setRoleId(roleId);
            bindRole.setCreateTime(new Date());
            bindRole.setUpdateTime(new Date());
            bindRole.setIsDel(0);

            boolean saveResult = accountBindRoleDAO.save(bindRole);
            if (!saveResult) {
                LogUtil.error(log, "OperationAdminUserManagerServiceImpl.bindUserRole >> 绑定用户角色失败");
                throw new InternalException(ErrorCodeEnum.DATABASE_HANDLE_ERROR.getErrorCode(), "绑定用户角色失败");
            }

            LogUtil.info(log, "OperationAdminUserManagerServiceImpl.bindUserRole >> 绑定用户角色成功");
        } catch (Exception e) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.bindUserRole >> 绑定用户角色失败 >> error = {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新账号信息
     *
     * @param param        修改员工参数
     * @param existAccount 现有账号信息
     */
    private void updateAccount(UserModifyParam param, AccountDO existAccount) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.updateAccount >> 开始更新账号信息 >> userId = {}", param.getUserId());

        try {
            AccountDO updateAccount = new AccountDO();
            updateAccount.setUserId(param.getUserId());

            // 只更新有值的字段
            if (StrUtil.isNotBlank(param.getUsername())) {
                updateAccount.setAccount(param.getUsername());
            }
            if (StrUtil.isNotBlank(param.getName())) {
                updateAccount.setName(param.getName());
            }
            if (StrUtil.isNotBlank(param.getPhone())) {
                updateAccount.setPhone(param.getPhone());
            }
            if (param.getAccountStatus() != null) {
                updateAccount.setAccountStatus(param.getAccountStatus());
            }

            boolean updateResult = accountDAO.updateByUserId(updateAccount);
            if (!updateResult) {
                LogUtil.error(log, "OperationAdminUserManagerServiceImpl.updateAccount >> 更新账号信息失败");
                throw new InternalException(ErrorCodeEnum.DATABASE_HANDLE_ERROR.getErrorCode(), "更新账号信息失败");
            }

            LogUtil.info(log, "OperationAdminUserManagerServiceImpl.updateAccount >> 更新账号信息成功");
        } catch (Exception e) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.updateAccount >> 更新账号信息失败 >> error = {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新用户角色关系
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    private void updateUserRole(String userId, String roleId) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.updateUserRole >> 开始更新用户角色 >> userId = {}, roleId = {}", userId, roleId);

        try {
            AccountBindRoleDO updateRole = new AccountBindRoleDO();
            updateRole.setUserId(userId);
            updateRole.setRoleId(roleId);

            accountBindRoleDAO.updateRoleIdByUserId(updateRole);

            LogUtil.info(log, "OperationAdminUserManagerServiceImpl.updateUserRole >> 更新用户角色成功");
        } catch (Exception e) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.updateUserRole >> 更新用户角色失败 >> error = {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 构建用户详情查询结果
     *
     * @param accountDO 账号信息
     * @param userRole  用户角色关系
     * @param roleName  角色名称
     * @return 用户详情查询结果
     */
    private UserDetailQueryResult buildUserDetailQueryResult(AccountDO accountDO, AccountBindRoleDO userRole, String roleName) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.buildUserDetailQueryResult >> 开始构建用户详情结果");

        UserDetailQueryResult result = new UserDetailQueryResult();
        result.setUserId(accountDO.getUserId());
        result.setUsername(accountDO.getAccount());
        result.setName(accountDO.getName());
        result.setPhone(accountDO.getPhone());
        result.setAccountStatus(accountDO.getAccountStatus());

        if (userRole != null) {
            result.setRoleId(userRole.getRoleId());
        }
        result.setRoleName(roleName);

        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.buildUserDetailQueryResult >> 构建用户详情结果完成");
        return result;
    }

    /**
     * 创建运营用户信息
     *
     * @param param    新增员工参数
     * @param tenantId 租户ID
     * @param userId   用户ID
     */
    private void createOperationUser(UserAddParam param, String tenantId, String userId) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.createOperationUser >> 开始创建运营用户信息 >> userId = {}", userId);

        try {
            OperationUserDO operationUserDO = new OperationUserDO();
            operationUserDO.setTenantId(tenantId);
            operationUserDO.setUserId(userId);
            operationUserDO.setUsername(param.getUsername());
            operationUserDO.setUserType("OTHER"); // 默认为员工类型
            operationUserDO.setCreateTime(new Date());
            operationUserDO.setUpdateTime(new Date());
            operationUserDO.setIsDel(0);

            boolean saveResult = operationUserDAO.save(operationUserDO);
            if (!saveResult) {
                LogUtil.error(log, "OperationAdminUserManagerServiceImpl.createOperationUser >> 保存运营用户信息失败");
                throw new InternalException(ErrorCodeEnum.DATABASE_HANDLE_ERROR.getErrorCode(), "保存运营用户信息失败");
            }

            LogUtil.info(log, "OperationAdminUserManagerServiceImpl.createOperationUser >> 创建运营用户信息成功");
        } catch (Exception e) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.createOperationUser >> 创建运营用户信息失败 >> error = {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新运营用户信息
     *
     * @param param 修改员工参数
     */
    private void updateOperationUser(UserModifyParam param) {
        LogUtil.info(log, "OperationAdminUserManagerServiceImpl.updateOperationUser >> 开始更新运营用户信息 >> userId = {}", param.getUserId());

        try {
            // 只有用户名变更时才需要更新运营用户表
            if (StrUtil.isNotBlank(param.getUsername())) {
                OperationUserDO updateOperationUser = new OperationUserDO();
                updateOperationUser.setUserId(param.getUserId());
                updateOperationUser.setUsername(param.getUsername());
                updateOperationUser.setUpdateTime(new Date());

                boolean updateResult = operationUserDAO.lambdaUpdate()
                        .eq(OperationUserDO::getUserId, param.getUserId())
                        .set(OperationUserDO::getUsername, param.getUsername())
                        .set(OperationUserDO::getUpdateTime, new Date())
                        .update();

                if (!updateResult) {
                    LogUtil.error(log, "OperationAdminUserManagerServiceImpl.updateOperationUser >> 更新运营用户信息失败");
                    throw new InternalException(ErrorCodeEnum.DATABASE_HANDLE_ERROR.getErrorCode(), "更新运营用户信息失败");
                }

            } else {
                LogUtil.info(log, "OperationAdminUserManagerServiceImpl.updateOperationUser >> 用户名未变更，无需更新运营用户信息");
            }
        } catch (Exception e) {
            LogUtil.error(log, "OperationAdminUserManagerServiceImpl.updateOperationUser >> 更新运营用户信息失败 >> error = {}", e.getMessage(), e);
            throw e;
        }
    }
}
