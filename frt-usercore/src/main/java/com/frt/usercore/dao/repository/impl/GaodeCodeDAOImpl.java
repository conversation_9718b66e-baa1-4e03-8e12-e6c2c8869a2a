package com.frt.usercore.dao.repository.impl;

import cn.hutool.core.util.StrUtil;
import com.frt.usercore.dao.entity.GaodeCodeDO;
import com.frt.usercore.dao.mapper.GaodeCodeMapper;
import com.frt.usercore.dao.repository.GaodeCodeDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 地址信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
public class GaodeCodeDAOImpl extends ServiceImpl<GaodeCodeMapper, GaodeCodeDO> implements GaodeCodeDAO {

    @Override
    public List<GaodeCodeDO> findListByCodeAndLevel(String provinceCode, String cityCode, Integer level) {
        return lambdaQuery()
                .eq(StrUtil.isNotBlank(provinceCode), GaodeCodeDO::getProvince, provinceCode)
                .eq(StrUtil.isNotBlank(cityCode), GaodeCodeDO::getCode, cityCode)
                .eq(GaodeCodeDO::getLevel, level)
                .orderByDesc(GaodeCodeDO::getSort)
                .list();
    }

    @Override
    public GaodeCodeDO getInfoByCode(String code) {
        return lambdaQuery()
                .eq(GaodeCodeDO::getCode, code)
                .one();
    }
}
